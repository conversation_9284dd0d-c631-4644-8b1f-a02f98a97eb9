模块名：cc-quality
版本号:  3.5#20250826-1
修改日期: 2025-08-26
修改人:  廖翊楷
脚本:  3-ycbusi_cc_quality_update_mysql_v3.5.sql
说明:
    1、优化人工质检-存在问题统计与智能质检-存在问题统计报表统计查询方法
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250805-1
修改日期: 2025-08-19
修改人:  施哲瀚、廖翊楷
脚本:  无
说明:
    1、工单质检详情界面录音组件优化，展示号码和通话时间
    2、优化质检任务检测关闭逻辑优化
    3、修复工单质检详情界面录音组件展示号码异常问题
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250804-1
修改日期: 2025-08-05
修改人:  廖翊楷、施哲瀚
脚本:  无
说明:
    1、任务抽取规则显示新增展示单位
    2、删除客户消息量与坐席回复量字段
    3、QC_CHANNEL_TYPE字典修复
    4、交互次数不低于逻辑优化
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250723-1
修改日期: 2025-07-28
修改人:  廖翊楷、施哲瀚
脚本:  无
说明:
    1、新增“增加交互次数，在线客服可以配置剔除低于3次交互次数的对话；
    2、任务抽取规则全渠道支持配置抽检周期，可选择早上（08:30-12：00），下午（12:00-18:00时），晚上（18：00-22:00时）
    3、任务抽取规则全渠道支持配置每月每人不少于x条数据
    4、取消语音渠道单次抽取数量限制，调整定时任务频率
    5、OrderMessageByPhone接口支持过滤通话时长为0的通话
    6、随机抽取质检工单优化
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250722-1
修改日期: 2025-07-22
修改人:  施哲瀚
脚本:  无
说明:
    1、新增抽取规则--使用弹窗；
	2、回显问题处理；
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250718-1
修改日期: 2025-07-21
修改人:  施哲瀚
脚本:  无
说明:
    1、规则配置页面使用二级页面
	2、修改通话时长规则，界面与后台逻辑优化
	3、规则-工单数据源默认工单主数据源
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250716-1
修改日期: 2025-07-16
修改人:  廖翊楷
脚本:  无
说明:
    1、修复质检任务配置【人工质检目标】和【智能质检目标】不能同时是100%问题
    2、修复全媒体渠道质检员抽取失败问题
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250715-1
修改日期: 2025-07-15
修改人:  施哲瀚
脚本:  无
说明:
    1、修复相同号码重复调用接口问题
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250701-1
修改日期: 2025-07-03
修改人:  施哲瀚、廖翊楷
脚本:  无
说明:
    1、任务抽取规则抽屉弹出添加属性wrapper-closable
    2、待质检列表获取渠道-任务数据（多级下拉）接口增加过滤逻辑，仅筛选启动或者暂停中的任务
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250701-1
修改日期: 2025-07-01
修改人:  施哲瀚
脚本:  无
说明:
    1、待质检清单-抽取规则新增（一级分类编码，申诉来源，工单紧急程度）
    2、智能质检结果新增命中话术字段
	3、管理质检任务-更多操作更改
	4、申诉判断去除分数判断
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250627-1
修改日期: 2025-06-25
修改人:  廖翊楷
脚本:  无
说明:
    1、待质检页面-“随机抽取质检工单”功能增加“是否为新员工”、“当前处理人”、“一级分类”、“申诉来源”、“工单重要程度”过滤条件
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250624-1
修改日期: 2025-06-25
修改人:   施哲瀚、廖翊楷
脚本:  无
说明:
    1、优化获取工单号码先后顺序
    2、优化我的质检页面-认可操作逻辑，保证数据更新
    3、优化工单界面-录音展示问题
    4、优化质检清单时间展示问题
    5、获取质检清单查询接口优化人工质检结果、智能质检结果筛选查询逻辑,工单号、投诉号码、工单创建时间来源优化
    6、修复质检详情界面智能质检结果错误问题
    7、我的质检结果查询接口优化人工质检结果、智能质检结果筛选查询逻辑
    8、修复工单质检查询sql，处理方字段由CREATE_ACC修改为HANDLE_AGENT_ACC
    9、优化语音渠道满意度信息入库逻辑
    10、优化已有智能质检结果更新逻辑
    11、全媒体条件抽取功能优化,解决无法按坐席进行抽取问题
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250623-1
修改日期: 2025-06-23
修改人:   施哲瀚、廖翊楷
脚本:  无
说明:
    1、列表姓名字段修改
	2、姓名字段查询修改
	3、质检申诉审批界面查询接口优化
	4、获取录音信息接口优化,增加加密手机号查询
	5、修复质检清单顶层视图统计指标应质检量错误问题
	6、坐席辅导管理页面、待质检清单、质检清单修改查询时间字段为“质检工单创建时间”,开放质检清单页面
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250620-1
修改日期: 2025-06-20
修改人:   施哲瀚、廖翊楷
脚本:  3-ycbusi_cc_quality_update_mysql_v3.5.sql
说明:
    1、质检/详情，智能质检结果展示
	2、优化统计问题界面展示
	3、优化列表来源展示
	4、修改字眼
	5、优化质检清单统计取值问题
	6、优化辅导列表界面传参问题
	7、优化工单界面获取录音信息提醒功能
	8、质检清单中查询质检任务接口增加权限控制,管理员账号支持查询全部数据
	9、修复语音质检详情界面中查询录音接口报错以及近一个月工单数查询数据错误
	10、修复辅导清单查询数据接口逻辑
	11、获取质检清单顶层视图统计指标接口修复
	12、修复质检清单-日志清单类界面：抽取结果、抽取结果描述、执行机器MAC字段返回默认为空问题
	13、修复任务执行日志界面，执行明细返回都是空的问题
	14、修复质检清单、我的质检、坐席辅导审批3个界面：姓名查询应该返回“名称”；现在返回的是账号，没有返回名称
	15、修复人工质检-存在问题统计、智能质检-存在问题统计2个界面:姓名字段应该返回“名称”；现在返回的是账号
	15、智能质检结果筛选扣分项
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250619-1
修改日期: 2025-06-19
修改人:   施哲瀚、廖翊楷
脚本:  无
说明:
    1、质检清单界面优化
	2、申诉处理界面-展示问题优化
	3、详情界面（申诉、认可）后关闭界面
	4、质检对象详情接口新增智能质检结果
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250618-1
修改日期: 2025-06-18
修改人:   施哲瀚、廖翊楷
脚本:  无
说明:
    1、优化任务抽取规则-修改来源范围传参
	2、修改字眼
	3、调整任务管理界面搜索条件布局
	4、质检清单问题处理
	5、取消智能质检分数展示
	6、坐席辅导页面菜单路径修复 (需要升级cx-index)
	7、任务管理视图表头查询接口统计逻辑修复
	8、工单渠道按条件抽取功能修复
	9、获取质检清单顶层视图统计指标查询接口修复
	10、待质检清单查询接口增加权限控制，非管理员查看本身数据  管理员默认查全部的
	11、工单渠道质检与详情界面不需展示语音和工单的原链接
	12、任务管理-删除任务添加权限管理
	13、规则抽取配置-工单处理方只需在工单渠道展示
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250610-1
修改日期: 2025-06-10
修改人:   施哲瀚、廖翊楷
脚本:  3-ycbusi_cc_quality_update_mysql_v3.5.sql
说明:
    1、我的质检结果界面搜索条件改版
	2、主管审批、质检员审批、质检员二次审批页面展示列表改版
	3、工单展示录音信息，录音信息展示特定随路数据
	4、质检/详情界面添加申诉，认可按钮
	5、新增质检清单界面
	6、待质检列表改版
	7、优化改版内容
	8、质检清单查询接口优化,新增OBJ_STATE字段数据更新sql
	9、修改人工质检分组、任务抽取规则（渠道标签显示匹配问题）
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250603-1
修改日期: 2025-06-03
修改人:   廖翊楷
脚本:  3-ycbusi_cc_quality_update_mysql_v3.5.sql
说明:
    1、新增质检清单页面与查询接口
    2、质检对象记录表新增OBJ_STATE字段，记录全局质检记录生命周期,各生命流程更新状态
    3、修复MQ消费者线程释放后，无法抽取问题
    4、新增待质检清单查询接口
    5、日志清单类查询接口增加权限控制，管理员查询全部数据，普通质检员查询自身数据
    6、我的质检接口查询接口新增查询参数
    7、质检申诉审批表头重构,涉及主管审批、质检员审批、质检员二次审批页面
    8、新增辅导清单查询接口、通过客诉号码查询相关工单信息接口、通过客诉号码查询工单相关录音接口
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250526-1
修改日期: 2025-05-28
修改人:   施哲瀚
脚本:  无
说明:
    1、抽取数据功能调整，包括质检清单与待质检清单
	2、质检添加智能质检定位功能
	3、质检添加质检项描述
	4、删除质检评分功能
	5、工单内容的图标，增加文字说明，如图标显示为“工单内容”。
	6、申诉==详情页面新增认可、申诉的提交按键
	7、质检申诉审批==》添加申诉内容展示
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250526-1
修改日期: 2025-05-28
修改人:   廖翊楷、施哲瀚
脚本:  3-ycbusi_cc_quality_update_mysql_v3.5.sql
说明:
    1、新增质检数量统计报表查询接口
    2、新增问题统计报表查询接口,支持分页查询
    3、智能质检结果记录表新增渠道类型以及坐席信息字段，在智能质检结果回调中进行入库
	4、质检任务新增/编辑界面改造；
	5、任务管理界面表头修改，操作修改，统计指标修改；
	6、新增三个统计报表界面（质检全量统计、问题统计表（人工/智能））；
	7、新增接口与页面添加权限控制
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250523-1
修改日期: 2025-05-23
修改人:   廖翊楷
脚本:  无
说明:
    1、各渠道渠道抽取数据到质检池逻辑优化，以工单为维度，并增加各来源筛选
    2、质检管理-任务管理-列表表头以及查询接口优化
    3、质检管理-任务管理-列表操作-新增作废任务
	4、任务抽取规则-添加工单处理方
	5、新增任务-修改成一个界面完成配置
	6、新增任务管理视图表头查询接口
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250519-2
修改日期: 2025-05-19
修改人:   廖翊楷
脚本:  3-ycbusi_cc_quality_update_mysql_v3.5.sql
说明:
    1、全媒体渠道抽取规则删除服务开始时间、服务结束时间、服务时长字段,新增坐席回复量、客户消息量字段,实现剔除低于3次交互次数的对话
    2、语音渠道抽取规则新增呼叫方式字段,新增对应sql构建方法
    3、工单渠道中流程新增“质检抽取字段”配置按钮，控制工单抽取字段是否使用;
    4、任务抽取规则新增“工单处理方”字段配置，控制抽取人工还是RPA机器人创建的工单
    5、任务管理-新增任务中，坐席被质检员抽取数（为空不限制）该配置项修改输入框输入，不做范围选择
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250519-1
修改日期: 2025-05-19
修改人:   施哲瀚
说明:
    1、任务抽取规则:添加来源范围,且工单来源需要多选。
	2、任务抽取规则:新增时工单流程接口添加启动状态筛选条件.
	3、任务管理-新增任务中，坐席被质检员抽取数（为空不限制）该配置项修改输入框输入，不做范围选择。
	4、人工质检分组改成人工质检员配置，把质检对象去掉
	5、新增任务界面质检组=>质检员组
	6、参数配置界面,删除质检分数配置
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20250516-1
修改日期: 2025-05-16
修改人:   廖翊楷
说明:
    1、外部调用录音下载播放接口增加鉴权与盐值加密
    2、更新字典组 QC_CHANNEL_TYPE
    3、修复MQ消费者线程无法释放的问题
    4、工单模块 getWfProcessList() 增加启用状态筛选
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  爱音乐版本-1
修改日期: 2025-04-24
修改人:   廖翊楷、施哲瀚
说明:
    1、 优化语音质检界面布局
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  爱音乐版本-1
修改日期: 2025-04-23
修改人:  施哲瀚
说明:
    1、 优化语音质检界面布局
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  爱音乐版本-1
修改日期: 2025-04-16
修改人:   廖翊楷
说明:
    1、 动态时间范围抽取-游标优化
    2、 人工质检结果工单渠道数据查询优化
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  爱音乐版本-1
修改日期: 2024-10-21
修改人:   廖翊楷、施哲瀚
说明:
    1、 语音渠道与全媒体渠道新增挂断类型与排队时间智能质检
    2、 智能质检新增复合质检(将话单记录或全媒体记录对应的工单信息一起推送智能模型进行质检)
    3、 全媒体渠道推送获取录音排队时长定制化
    4、 智能质检任务推送-从任务层面上剔除掉仅人工质检
    5、 智能质检推送-语音渠道渠道优先推送s3地址 防止部分转写失败
    6、 获取任务的坐席质检数量统计慢sql优化
    7、 修复打开工单质检详情页面报错，优化质检详情接口
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  爱音乐版本（在3.5#20240816-1后分离为定制版本，单独维护）
修改日期: 2024-09-02
修改人:   廖翊楷、施哲瀚
说明:
    1、我的质检页面暂未提供智能质检申述以及一系列后续逻辑，使用旧版本
    2、待质检列表 质检状态更新(与标准版不同)
    3、质检提交页面-添加分数提交异常保护
    4、工单渠道-智能质检推送数据内容优化
    5、语音全媒体渠道-质检员抽取数据优化
    6、“任务执行”修改为“人工质检”
    7、优化当质检申诉通过时且分数为100分时，上一次质检结果选择的质检项没有被更新掉
    8、全渠道任务数据明细页面-搜索条件-默认值修改
    9、主管或质检员申诉点击退回后，页面不会自动关闭修复
    10、质检申诉-提交申诉-附件管理，优化上传的文件展示在外层
        (需要依赖cc-base的getAttachmentMsg()方法以及,以及升级 easitline-cdn)
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240816-1
修改日期: 2024-08-16
修改人:   廖翊楷、林佳兴、施哲瀚
说明:
	1、人工质检规则模板优化，对应导出导入方法更新
	2、待质检列表增加任务名称、质检时间、创建时间、质检状态、受理号码、渠道唯一ID、等级评定，
	    同时质检对象、受理号码、渠道唯一ID等筛选条件，同时“释放”质检记录增加权限控制
	3、人工质检页面-分数与评定等级联动，根据结果分数自动展示对应的评定等级；说明信息框前需要
	    增加小标题“存在问题详情”；根据系统配置项控制人工质检页面坐席辅导填写表单是否展示
	4、人工质检页面-质检申诉-申诉处理-申诉内容外层展示
	5、坐席辅导页面-展示辅导信息-辅导原因说明 允许填写
	6、质检申述流程更改：我的质检结果-质检完成后或第一次质检申述退回后，新增“确认”按钮，可直
	    接进行结案，第二次质检申述退回后，系统立即进行结案，不提供“确认”按钮
	7、质检-任务管理-创建任务信息，质检目标非必填
	8、质检流转es追踪日志
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240718-1
修改日期: 2024-07-25
修改人:   廖翊楷、林佳兴、施哲瀚
说明:
	1、解决相关质检报表数据异常以及导出问题
	2、解决编辑质检任务时预览质检组时，质检员只有一位情况
	3、第三方渠道抽取规则优化
	4、人工质检结果分析报表页面与质检详情页面新增实际分数字段
	5、第三方渠道入库表新增OBJECT_ID业务字段，入库逻辑优化,方便业务报表导出
	6、新增质检不合格的辅导学习流程
	7、工单渠道质检员抽取新增根据智能质检筛选分数抽取
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240708-1
修改日期: 2024-07-16
修改人:   廖翊楷、林佳兴、施哲瀚
说明:
	1、任务抽取日志优化(CC_QC_TASK_LOG 全渠道记录)
	2、优化质检任务抽取逻辑，当对应质检组没有设置质检对象时，默认抽取全部坐席数据
	3、申述逻辑优化，一次申述必定提交给对应质检员，二次申述根据是否开启组长审核提交给组长或质检员
	4、质检组配置界面优化、任务执行抽取界面提供全局用户搜索
	5、坐席待质检列表里，增加随机抽取5条、按条件抽取、我已质检按钮
	6、一键删除质检组优化、添加质检组成员时展示部门信息与搜索条件
	7、解决任务执行-查看抽取记录时间组件默认值问题、解决待质检列表工单展示重复问题
	8、第三方渠道抽取按质检组过滤优化、创建智能质检模型兼容自定义渠道
	9、质检员抽取数据逻辑优化，兼容任务停止与删除场景、通话分析百分比优化
	tip：升级该版本需执行脚本(CC_QC_TASK_LOG 增加 EX_JSON字段)
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240628-1
修改日期: 2024-06-28
修改人:   刘杰、廖翊楷
说明:
	1、工单任务抽取日志优化
	2、工单渠道抽取数据状态更新
	3、语音智能质检 不开启离线质检对接
	4、修复待质检列表中智能质检分数显示为0
	5、新增第三方渠道智能质检推送
	6、质检任务统计导出异常修改
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240625-4
修改日期: 2024-06-25
修改人:   廖翊楷、林佳兴、施哲瀚
说明:
	1、第三方入库CC_QC_THIRD_RECORD表扩展字段增加/脚本增加
	2、Constants.getBusiId() 业务ID配置化
	3、抽取规则树形结构匹配
	4、质检任务统计导出优化
	5、第三方详情页面 链接挂载
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240615-3
修改日期: 2024-06-15
修改人:   廖翊楷、林佳兴、施哲瀚
说明:
	1、修复任务管理-智能质检目标率展示错误
	2、修复工单质检页面提交后没有自动打开下一条工单的质检页面
	3、修复组长处理申诉时，查看到的申诉说明为第一次申诉时的说明
	4、修复组长处理工单渠道数据的二次申诉时，点击申诉对比无数据情况
	5、修复查看我的质检结果详情时，人工质检结果显示为空白问题
	6、修复质检任务统计tab页与“任务管理”菜单页的任务状态不一致情况
	7、修复申诉情况分析菜单无法正常打开一直在转圈
	8、修复新增“仅智能质检任务”，点击【下一步】时提示“请至少选择一个主流程”。但是页面上又没有选择主流程的选项情况
	9、修复任务配置-智能质检规则页面-修改、标签配置、添加模型无反应情况
	10、修改质检数据抽取页面重置搜索条件后，无法更改任务状态情况
	11、修复当工单质检结果已存在，不再推送智能质检，将已存在的结果当做当前记录的智能质检结果时，没有同步智能质检分数与时间的情况
	12、修复人工质检分组中已配置的分组，重置后渠道是不可编辑的状态/任务抽取规则：新增规则页面会缓存配置为空的规则数据两种情况
	13、修复新建任务页面进行新增抽取规则，配置重复的规则报提示后，【确定】按钮无法使用/新建任务页面进行新增抽取规则，预览按钮与抽取规则重叠
	14、优化任务分配：分配质检员页面中的角色查询条件建议允许置空/人工质检抽取记录：搜索条件中默认的时间条件，开始时间大于结束时间
	15、质检统计--申述通过数量筛选条件优化/质检员质检分析-申述数、申述通过数、申述率、申述通过率优化/申诉情况分析报表sql优化，取消关联CC_BUSI_USER，删除质检员名称字段
	16、质检三阶段看板查询逻辑优化/“质检管理-统计查询-质检汇总报表”菜单平均分计算方式优化
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240527-2
修改日期: 2024-06-09
修改人:   廖翊楷、张雪飞、施哲瀚
说明:
	1、新增质检员抽取新增百分比抽取方式
	2、配置项取值优化、兼容金蝶mars改造
	3、兼容gov-portal 质检任务配置页面优化
	4、修复申诉流程流转不正确情况：一次申诉>>审批不通过，直接变为二级申诉退回
	5、南昌12345问题单修复、爱音乐生产环境测试问题单修复
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5#20240516-1
修改日期: 2024-05-16
修改人:   刘杰
说明:
	1、调整菜单路径、增加接口权限； 注意：升级后，菜单会有变化，需要重新分配菜单权限；另外该版本已适配达蒙数据库，需要升级mars3.4
	2、整体工单/全媒体质检报表导出功能优化
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.5.1#20240506-1
修改日期: 2024-05-06
修改人:   廖翊楷
说明:
	1、新增质检三阶段数据看板:质检整体分析、坐席质检分析、质检员质检分析、质检组质检分析、坐席个人画像分析
	2、质检三阶段数据看板接口上线
	3、爱音乐问题单修复
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20240509-1
修改日期: 2024-05-09
修改人:  张雪飞
说明：
		1、增加达梦数据库支持；
		2、增加金蝶中间件支持；
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20240228-1
修改日期: 2024-02-28
修改人:   张丹
说明:
	1、质检界面关联显示工单、在线、录音、邮件信息
	2、人工质检结果新增离线导出(可以导出评语和规则明细)
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20240220-1
修改日期: 2024-02-20
修改人:   张丹
说明:
	1、质检中评语从200改为支持500,需要执行修改字段长度sql
	2、人工质检结果页面显示、导出(评语和案例类型)
         3、质检新增配置项：支持申诉x天内评分的质检记录
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20240102-1
修改日期: 2024-01-02
修改人:   陈建立
说明:
	1、兼容数据库加密查询
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20231225
修改日期: 2023-12-25
修改人:   陈建立
说明:
	1、增加智能质检目标 需执行20231225数据库脚本
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20231207
修改日期: 2023-12-07
修改人:   李滢
说明:
	1、删除定时处理录音转写相关接口以及企业参数中相关的配置项
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20231201
修改日期: 2023-12-01
修改人:   陈建立
说明:
	1、质检整体界面风格切换为vue
	2、增加申诉对比功能
	3、人工质检结果、智能质检结果合并为质检结果菜单(tab页实现)
	4、废弃案例库，将优秀案例、警示案例放入到质检结果列表筛选
	5、质检员统计、质检任务统计、质检对象统计(坐席质检统计)合并为质检统计(tab页实现)
	6、质检申诉菜单上移并调整为“我的质检结果”
	7、质检申诉审批查看详情界面支持操作 通过、退回、转派按钮
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.4.2#20230817
修改日期: 2023-08-17
修改人:   陈建立
说明:
	1、修改申诉流程接口，需执行v3.4.2最新脚本
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.1#20230815
修改日期: 2023-08-15
修改人:   陈建立
说明:
	1、对接智能质检任务推送、模型列表接口
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.1#20230324
修改日期: 2023-03-24
修改人:   陈建立
说明:
	1、增加智能分配 tab 
	2、增加智能回收 tab 
	需要执行20230519及之后脚本
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.1#20230324
修改日期: 2023-03-24
修改人:   陈建立
说明:
	1、质检首页指标数据服务
	2、需同步升级cc-statgw
	需要执行2023-03-24脚本
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:  3.1#20230109
修改日期: 2022-11-24
修改人:   张丹
说明:
	1、质检详情界面增加需辅导字段
	2、人工质检查询列表增加需辅导字段
	需要执行2023-01-09脚本
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20221124
修改日期: 2022-11-24
修改人:   张丹
说明:
	1、修改质检获取订购id的方法，适配12345，增加参数配置业务id
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220901
修改日期: 2022-09-01
修改人:   刘稳
说明:
	1、将StringUtils.parseInt 改为CommonUtil.parseInteger 空值会返回null，只应用EasySql.append方法
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220822
修改日期: 2022-08-22
修改人:   冼志泳
说明:
	1、质检3.4版本优化代码
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220819
修改日期: 2022-08-19
修改人:   刘稳
说明:
	1、将系统适配pgsql数据库
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220718
修改日期: 2022-07-18
修改人:   陈志彬
说明:
	1、录音文件整合oss云存储
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220629
修改日期: 2022-06-29
修改人:   邱泽宏
说明:
	1、修复统计分析界面无数据时展示为空的问题;
    2、修复日志查询异常问题
    3、修复任务监控统计数据跟任务质检页面不一致的问题
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220623-1
修改日期: 2022-06-23
修改人:   冼志泳
说明:
	1、RegInitDataService  改为 继承 BaseRegInitDataService
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220614-1
修改日期: 2022-06-14
修改人:   张丹
说明:
	1、修改人工质检分组，质检对象查询条件，重复显示坐席账号
	2、修改人工质检规则，评分细项名称限制输入字数
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220530-1
修改日期: 2022-05-30
修改人:   许诚誉
说明:
	1、邮件质检界面和邮件详情界面
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220510-1
修改日期: 2022-05-16
修改人:   林佳兴
说明:
	1、任务明细数据链接调整为新版ui地址；
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220510-1
修改日期: 2022-05-10
修改人:   邱泽宏
说明:
	1、调整任务管理界面菜单路径
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220426-1
修改日期: 2022-04-28
修改人:   林佳兴
说明:
	1、新版质检管理界面优化及国际化
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220426-1
修改日期: 2022-04-26
修改人:   邱泽宏
说明:
	1、添加质检任务流程追踪日志
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220225-1
修改日期: 2022-04-21
修改人:   林佳兴
说明:
	1、新增质检管理新版UI界面和升级cc-portal
--------------------------------------------------------------------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220316
修改日期: 2022-03-16
修改人:   张丹
说明:
	1、修改质检报表导出（查询统计表）需要更新脚本和升级cc.statgw
--------------------------------------------------------------------------------------------------------------------------------------------------

模块名：cc-quality
版本号:   3.1#20220225
修改日期: 2022-02-25
修改人:   邱泽宏
说明:
	1、修复接口垂直越权问题
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20220105
修改日期: 2022-01-05
修改人:   陈建立
说明:
	1、质检任务增加质检员是否质检自身数据  (需执行脚本cc-base 20220105)
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211231-1
修改日期: 2021-12-31
修改人:   陈建立
说明:
	1、抽取规则日期类型增加上周选择
	2、修改全媒体话单抽取方法，将ent_time改为begin_time
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211209-1
修改日期: 2021-12-09
修改人:   潘以宝
说明:
	1、质检，结果评定选择多级评定，完成质检后，查看质检详情，多级评定项没有做禁止选项
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211208-1
修改日期: 2021-12-08
修改人:   潘以宝
说明:
	1、无锡农商行-智能质检接口要求推送时，把数据库的录音路径切掉，只留文件名，并拼接上时间戳
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211118-1
修改日期: 2021-11-18
修改人:   潘以宝
说明:
	1、添加质检规则评分细项类型
	ALTER TABLE CC_QC_ITEM ADD COLUMN SCORE_TYPE  int(5) DEFAULT -1 COMMENT '评分细项类型-1减分，1加分';
	update CC_QC_ITEM set SCORE_TYPE=1 where STD_SCORE > 0;
	update CC_QC_ITEM set SCORE_TYPE=-1 where STD_SCORE < 0;
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211111-1
修改日期: 2021-11-11
修改人:   潘以宝
说明:
	1、全媒体抽取规则里增加抽取有效会话的条件
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211108-1
修改日期: 2021-11-08
修改人:   潘以宝
说明:
	1、全媒体质检详情添加侧边栏，补充部分基础信息
	2、点击立即执行后调用任务统计服务统计数据
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211105-1
修改日期: 2021-11-05
修改人:   潘以宝
说明:
	1、统计分析图表描述添加国际化
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211104-1
修改日期: 2021-11-04
修改人:   潘以宝
说明:
	1、质检规则导出导入功能缺少评分方式
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211027-1
修改日期: 2021-10-27
修改人:   潘以宝
说明:
	1、智能质检全媒体消息过滤空内容
	2、质检员批量释放数据时如果存在已质检数据不能释放
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20211008-1
修改日期: 2021-10-08
修改人:   潘以宝
说明:
	1、质检规则添加坐席原始数据抽取率限制
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210928-1
修改日期: 2021-09-28
修改人:   潘以宝
说明:
	1、质检时，鼠标移动到质检规则上显示，添加质检规则建议图表，移到图标上是提示'针对该质检规则提出建议'
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210924-1
修改日期: 2021-09-24
修改人:   潘以宝
说明:
	1、页面删除js debugger
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210918-1
修改日期: 2021-09-17
修改人:   潘以宝
说明:
	1、添加转写完成后是否将录音转成MP3
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210917-1
修改日期: 2021-09-17
修改人:   潘以宝
说明:
	1、添加质检数据图表分析
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210916-1
修改日期: 2021-09-16
修改人:   潘以宝
说明:
	1、质检数据总览Sql兼容oracle
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210914-1
修改日期: 2021-09-14
修改人:   潘以宝
说明:
	1、修改自动按周分配week函数Oracle兼容
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210906-1
修改日期: 2021-09-06
修改人:   潘以宝
说明:
	1、添加质检任务统计，优化查询速度
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210826-3
修改日期: 2021-08-26
修改人:   潘以宝
说明:
	1、质检-人工质检结果-质检结果明细-新增字段（会话编号）
	2、质检-全媒体质检数据明细界面-新增字段（会话编号）
	3、质检-全媒体质检数据明细界面-新增满意度 字段
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210824-1
修改日期: 2021-08-24
修改人:   潘以宝
说明:
	1、优化定时任务抽取sql
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210823-2
修改日期: 2021-08-23
修改人:   潘以宝
说明:
	1、坐席质检数量限制添加按天限制
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210823-1
修改日期: 2021-08-23
修改人:   潘以宝
说明:
	1、申诉通过修改页面添加提交按钮
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210820-1
修改日期: 2021-08-20
修改人:   冼志泳
说明:
	1、质检管理的统计查询的统计分析添加了个质检员整体质检分析tab页
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210812-1
修改日期: 2021-08-12
修改人:   冼志泳
说明:
	1、查询录音质检统计页面报错
	2、导出录音质检统计报表报500错误
	3、查询录音质检统计页面报错
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210725-1
修改日期: 2021-07-25
修改人:   曾晖
说明:
	1、全媒体质检统计优化
	2、话务质检统计优化
	3、坐席质检统计(包含4个菜单、4张报表)
	4、质检员质检统计(包含2个菜单、2张报表)
	5、评定等级配置界面，限制最多配置3级
	6、评定等级报表
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210712-1
修改日期: 2021-07-12
修改人:   潘以宝
说明:
	1、统计分析里，目前有2个tab，智能质检统计、人工质检统计，修改为：人工质检整体分析、智能质检统计、人工质检统计、坐席整体质检分析、质检员整体质检分析、坐席抽检分析、质检员工作分析；打开界面时不要加载每个tab页的数据，点击tab时才加载数据，默认加载第一个tab的数据
	2、人工质检整体分析：
	2.1）质检数据总览（创建时间）：顶部用数据块分两行显示， 质检总数、质检比例、质检平均分、质检任务数、待质检数、话务质检数、话务质检平均分，全媒体质检数、全媒体质检平均分
	2.2）质检数量分析（创建时间），使用柱状图图，显示选择区间内，每天的质检数量（包含两条折线，话务质检数、全媒体质检数）
	2.3）质检抽取分析，再下面使用折线图，显示选择区间内，每天的抽检总量（包含两条折线，话务抽检数、全媒体抽检量数）
	2.4）评定等级分布，使用柱状图，显示话务、全媒体一级评定等级的分布对比
	2.5）质检申诉分析，使用两条折线图，统计每天的发起申诉量、申诉审核通过量
	3、质检评定判断是否存在是添加企业ID条件
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210707-1
修改日期: 2021-07-07
修改人:   潘以宝
说明:
	【oracle】质检查询统计中的统计分析“质检项排行、得分分布、分数等级”没有数据	陈君淑
	【oracle】全媒体坐席统计报表中，“按质检员、按日期”统计条件查询没有数据	陈君淑
	质检员维护中，“地址、简介、创建人账号”三个字段是否有实际用处	陈君淑
	在任务分配中，手工分配质检员后，在全媒体质检数据中“抽取方式”显示为空	陈君淑
	质检结果明细报表中，“通话/会话记录ID”搜索条件无效	陈君淑
	质检申诉中，开启组长审批申诉，把质检申诉转派给A坐席，其他坐席也能看到该条申诉	陈君淑
	质检管理中的结果评定等级界面，标签名称搜索条件查询无效
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210706-1
修改日期: 2021-07-06
修改人:   潘以宝
说明:
	1、如果开启了申诉组长审批，组长已经指定了质检员，二次申诉审核页面需要过滤
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210705-1
修改日期: 2021-07-05
修改人:   潘以宝
说明:
	1、添加质检项单选
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210629-1
修改日期: 2021-06-29
修改人:   叶权辉
说明:
	1、完善 一键补抽 功能 ，现功能可针对每个质检对象进行补抽
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210622-1
修改日期: 2021-06-22
修改人:   潘以宝
说明:
	1、【oracle】人工质检分组，ORA-00904: "GROUP_CONCAT": invalid identifier
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210617-1
修改日期: 2021-06-17
修改人:   潘以宝
说明:
	1、bug 6031 任务执行，我已质检，质检结果明细页面，未显示质检员本人的质检数据
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210616-1
修改日期: 2021-06-16
修改人:   潘以宝
说明:
	1、bug 6029 质检任务，待人工质检数据页面，更换质检员窗口应显示当前质检任务已分配的质检员
	2、优化任务明细数据查找速度
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210615-1
修改日期: 2021-06-15
修改人:   潘以宝
说明:
	1、bug 6025 人工质检分组，质检对象维护页面，添加按钮和弹框建议改为“添加质检对象”
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210611-1
修改日期: 2021-06-11
修改人:   潘以宝、叶权辉
说明:
	1、bug 5989 全部规则建议页面，查询列表的是否采纳修改为是和否
	2、bug 5990 质检的(是否开启二次申诉)参数配置统一开关
	3、bug 5992 统计分析，一票否决命中次数排行，统计次数不准确
	4、开启手动评分的情况下，补充关联知识按钮
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210609-1
修改日期: 2021-06-09
修改人:   潘以宝
说明:
	1、质检员一键质检时可以提交到没有配置质检对象的任务中
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210608-1
修改日期: 2021-06-08
修改人:   潘以宝
说明:
	1、进入任务管理-质检任务页面，点击[语音质检0608_zy]任务的 操作 按钮，点击进入 任务明细数据 页面，查询列表，技能组列未显示队列名称
-------------------------------------------------------------------------
模块版本号:   3.0#20210607-1
最后修改日期: 2021-06-07
修改人:  陈建立
说明:
	1.任务执行增加垂直越权
	2.质检员抽取时将坐席数量限制提示添加都抽取记录描述里
---------------------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210603-1
修改日期: 2021-06-03
修改人:   潘以宝
说明:
	1、配置方式添加抽取规则呼叫创建原因
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210602-1
修改日期: 2021-06-02
修改人:   潘以宝
说明:
	1、全媒体质检列表服务结束原因和抽取规则不一致
	2、质检修改列表没有质检评定结果，内容是空的
	3、质检时间和服务时间同时存在是只留质检时间
	4、质检保存按钮给成暂存
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210601-1
修改日期: 2021-06-01
修改人:   潘以宝
说明:
	1、5729 给用户姓名带有"eval"和"^"的用户质检，提示“invalid access!”，不要在路径参数上传名称
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210531-1
修改日期: 2021-05-31
修改人:   潘以宝
说明:
	1、去掉质检项必选限制请
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210528-1
修改日期: 2021-05-28
修改人:   潘以宝
说明:
	1、bug 5254 质检结果明细页面显示的客户标识为空
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210526-1
修改日期: 2021-05-26
修改人:   潘以宝
说明:
	1、bug5703: 在我的通话记录和通话记录界面中进行质检，质检详情界面没有显示“结果评定”
	2、bug5729: 给用户姓名带有"eval"和"^"的用户质检，提示“invalid access!” 需要同步升级cc-report 3.1#20210526-1
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210525-1
修改日期: 2021-05-25
修改人:   潘以宝
说明:
	1、我的通话记录提交质检时如果已经有任务质检过需要给出提示
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210524-1
修改日期: 2021-05-24
修改人:   潘以宝
说明:
	1、任务抽取时传入企业参数，防止重复跑统一企业的数据
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210520-1
修改日期: 2021-05-20
修改人:   潘以宝
说明:
	1、进行质检的页面没有添加规则建议的图标
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210519-1
修改日期: 2021-05-19
修改人:   潘以宝
说明:
	1、调整菜单
	2、更换时间范围组件
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210512-1
修改日期: 2021-05-12
修改人:   潘以宝
说明:
	1、添加在线一键质检选择任务
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210510-1
修改日期: 2021-05-10
修改人:   潘以宝
说明:
	1、语音添加一键质检选择任务
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210508-1
修改日期: 2021-05-08
修改人:   潘以宝
说明:
	1、限制自动分配每次分配只分配当月的数据。
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210507-1
修改日期: 2021-05-07
修改人:   潘以宝
说明:
	1、质检员查找页面放宽日期限制，添加下拉快捷选择
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210506-1
修改日期: 2021-05-06
修改人:   潘以宝
说明:
	1、质检抽取规则限制数量
	2、结果评定等级不需要一键部署生成
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210430-1
修改日期: 2021-04-30
修改人:   潘以宝
说明:
	1、添加列表查询时间限制
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210428-1
修改日期: 2021-04-28
修改人:   潘以宝
说明:
	1、质检数据详情页面的基本信息添加显示隐藏按钮
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210427-1
修改日期: 2021-04-27
修改人:   潘以宝
说明:
	1、添加企业参数配置 语音质检数据：质检员不需要看到坐席信息和客户信息（包含坐席、话机号码、主叫、被叫）。质检详情的基本信息里也是隐藏掉。全媒体质检数据：质检员不需要看到坐席信息和客户信息（包含坐席、客户姓名）。质检详情的基本信息里也是隐藏掉。
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210426-1
修改日期: 2021-04-26
修改人:   潘以宝
说明:
	1、质检结果和质检结果明细需要区分数据权限(人工质检结果和质检结果明细权限跟随菜单“cc-qc-zjjg人工质检结果”，智能质检结果和质检结果明细权限跟随菜单“cc-qc-znzjjg智能质检结果”)
	2、质检申诉根据概要设计的流程，需要先到组长进行审核，但是该流程不支持。（概要设计里写需要支持） （"新增菜单“cc-qc-zzspss组长审批申诉” 列表支持数据权限" 将菜单开个组长）
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.1#20210425-1
修改日期: 2021-04-25
修改人:   潘以宝
说明:
	1、在线一键质检修改，需要同步升级cc-report3.0#202104125-1
	2、语音一键质检修改，需要同步升级cc-report3.0#202104125-1
	3、质检任务：质检组不全，无法选择二线质检组（过滤了质检员和质检坐席为空的质检组）
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210416-2
修改日期: 2021-04-16
修改人:   叶权辉
说明:
	1、关联知识点的功能，改成菜单权限控制开关
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210416-1
修改日期: 2021-04-16
修改人:   冼志泳
说明:
	1、修复进入质检-任务管理-人工质检规则页面，点击评分项配置链接，点击导出，未导出是否一票否决
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210415-1
修改日期: 2021-04-15
修改人:   潘以宝
说明:
	1、将chat-record.css从引用yc-media改完引用质检模块的版本，防止有现场不使用ye-media
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210413-1
修改日期: 2021-04-13
修改人:  叶权辉
说明:
	1、新增质检时，可关联知识点的功能：质检员在执行质检任务时，发现坐席对某个知识点不熟悉，可关联知识库的该知识点。坐席可在质检结果处查看。
	2、该功能需要同步升级cc-km 3.0#20210413-1 或以上版本
	3、该功能需要添加脚本：
	alter table CC_QC_RESULT add LINK_KM_JSON varchar(2000) comment '质检关联知识点的JSON';
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210412-1
修改日期: 2021-04-12
修改人:  潘以宝
说明:
	1、修改统计图表描述，'质检总数'为当天的'新增质检总数'
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210408-1
修改日期: 2021-04-08
修改人:  叶权辉
说明:
	1、解决 qc-result-info.jsp页面的导出时排序与页面不一致的问题：order by date_id是date_id相同可能引起排序问题
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210407-1
修改日期: 2021-04-07
修改人:  潘以宝
说明:
	1、 添加录音文件基本路径配置项，读取录音是优先取，为空时，默认读取webRoot的路径
	2、添加隐藏列表工单配置
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210326-2
修改日期: 2021-03-26
修改人:  潘以宝
说明:
	1、语音质检数据详情里人工质检完成数量统计错误
	2、智能质检结果列表显示坐席为空
	3、统计图表质检项排行统计错误
	4、智能质检明细项导出部分得分空白
	5、录音质检统计列表待质检数统计错误
	6、新版本智能质检结果，智能质检结果明细无法查到旧的数据，需要执行同步状态脚本
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210326-1
修改日期: 2021-03-26
修改人:  叶权辉
说明:
	1、修复质检时保存后，质检的基础分为0的bug
	2、兼容12345的查看工单
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210325-1
修改日期: 2021-03-25
修改人:  潘以宝
说明:
	1、人工质检明细质检项排序和预览不一致,原因：预览里有父子结构，明细里没有
	2、通知人工质检等分项时添加是否选中质检项判断，选中才是生效的分数
	3、智能质检明细导出部分得分项为空
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210323-1
修改日期: 2021-03-23
修改人:  潘以宝
说明:
	1、抽取字段添加挂断类型
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210322-1
修改日期: 2021-03-22
修改人:  潘以宝
说明:
	1、优化智能质检结果查询：使用cc_qc_task_obj的冗余字段查询坐席的名称和ID,可以减少两张关联表
	2、将挂机原因改为呼叫结果，与通话记录保持一致
	3、人工质检明细质检项排序和预览不一致
	4、智能质检明细，人工质检明细导出得分项空白：原因列表导出行数使用了企业参数配置的最大导出数entMaxRow
	      明细项的最大导出行数应该为entMaxRow*质检规则的等分项个数
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210319-2
修改日期: 2021-03-19
修改人:  潘以宝
说明:
	1、质检页面听录音时通过cc-base读无后缀
	2、智能质检项排序混乱的
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210319-1
修改日期: 2021-03-19
修改人:  潘以宝
说明:
	1、更改智能质检中状态
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210318-1
修改日期: 2021-03-18
修改人:  潘以宝,叶权辉
说明:
	1、修复抽取数据时，因为智能质检的状态错误，导致抽取不到数据的问题，已有旧数据的现场需执行以下脚本：
		update cc_qc_task_obj set ZN_STATE='4' WHERE ZN_STATE='3';
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210317-1
修改日期: 2021-03-17
修改人:  潘以宝
说明:
	1、质检时判断是否开启发布状态，开启将状态设置成待发布，不开启设置成已质检
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210316-2
修改日期: 2021-03-16
修改人:  叶权辉
说明:
	1、全媒体质检统计 查询和导出sql合并
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210316-1
修改日期: 2021-03-16
修改人:  潘以宝
说明:
	1、申诉审批无法打开
	2、质检组添加质检员无法选择
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210315-1
修改日期: 2021-03-15
修改人:  潘以宝
说明:
	1、抽取规则主叫，被叫添加等于判断
	2、质检组管理质检员选择列表查询sql错误
	3、手动分配和调配错误
-------------------------------------------------------------------------
模块名：cc-quality
版本号:   3.0#20210312-2
修改日期: 2021-03-12
修改人:  田博源
说明:
质检申诉审批操作栏中的“通过”/质检二次申诉审批操作栏中的“申诉修改”弹窗方式改为2弹，解决页面转圈圈问题
-------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#202103012-1
修改日期: 2021-03-12
修改人:  叶权辉
说明:
	1.修复 质检组分组的bug
	2.修复报表导出顺序以及符号的问题
	3.修复手工分配质检任务不成功的问题
-------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#202103011-1
修改日期: 2021-03-09
修改人:  叶权辉
说明:
	1.质检申诉字段 扩容为500，需更新脚本：
		ALTER TABLE CC_QC_RESULT_RECONSIDER modify DESCRIPTION1 varchar(1000) default NULL comment '第一次复议描述';
		ALTER TABLE CC_QC_RESULT_RECONSIDER modify DESCRIPTION2 varchar(1000) default NULL comment '第二次复议描述';
	2.统计报表导出的字段顺序调整
-------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210309-2
修改日期: 2021-03-09
修改人:  叶权辉
说明:
	1.语音质检 页面增加 挂机原因列，原 挂断类型 改为 结束挂断类型
-------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210303-1
修改日期: 2021-03-03
修改人:  叶权辉
说明:
	1.修复质检时,按质检状态查询时无效的bug
	2.增大申诉时的可输入字数，增至300
-------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210303-1
修改日期: 2021-03-03
修改人:  叶权辉
说明:
	1.修复质检时的页面数据显示的bug
	2.优化部分页面风格
-------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210301-1
修改日期: 2021-03-01
修改人:  叶权辉
说明:
	1.增加等级选择功能，同时有初始化数据脚本
	2.增加 全媒体和录音统计页面的 申诉成功数
	3.转派后，审核状态改为 2 （CC_QC_RESULT_RECONSIDER.STATE）
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210226-1
修改日期: 2021-02-26
修改人:  潘以宝
说明:
	任务分配界面优化
	任务调配界面优化
	发布界面优化
	结果修改界面优化
	添加系统自动分配质检任务
	添加开启自动分配企业参数配置
	添加优秀录音查询
	添加质检组质检员设置组长

---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210224-1
修改日期: 2021-02-24
修改人:  叶权辉
说明:
	1.增加对质检分组的人员进行启用、禁用操作；禁用后，相关地方的数据对该坐席进行屏蔽
	2.增加 是否允许同一坐席属于同一类型下的不同质检分组 的开关，开关在企业参数中配置
	3.申诉时 新增 退回操作
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210223-2
修改日期: 2021-02-23
修改人:  潘以宝
说明:
	1.抽取规则添加结束原因和结束原因空时的默认值
	2.坐席被抽检数修改优化
	3.抽取任务时间坐席id账号等信息查查到cc_qc_task_obj
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210223-1
修改日期: 2021-02-23
修改人:  叶权辉
说明:
	1.增加坐席最低质检数量：质检任务配置界面可配置坐席最低质检数量，质检员在语音质检时可一键补抽功能
	2.评分细则增加类型：评分细项增加字段选择关键字段（可配置：关键，一般关键，非关键），质检时展示该细项的关键值（颜色）。
	3.有新增字段 ：ALTER TABLE CC_QC_ITEM add  ITEM_TYPE varchar(20)  default '1' comment '质检项类型 1-一般 2-关键 3-非关键';
	4.部分界面优化
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210222-1
修改日期: 2021-02-22
修改人:  潘以宝
说明:
	1.添加服务单质检规则功能
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210220-1
修改日期: 2021-02-20
修改人:  潘以宝
说明:
	1.获取录音文件路径时判断文件名前是否带有/
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210204-1
修改日期: 2021-02-04
修改人:  蓝清潇
说明:
	1.质检管理界面优化
	2.分配质检员风格
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210203-3
修改日期: 2021-02-03
修改人:  蓝清潇
说明:
	1、人工质检分组->质检员维护界面风格改变，批量删除修改
	2、人工质检分组->质检对象维护界面风格改变，批量删除修改
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210203-2
修改日期: 2021-02-03
修改人:  潘以宝
说明:
	1、录音质检统计导出顺序不对
	2、智能质检导出改为最大1W条
	3、人工质检导出数据不全，sql建议改成和查询一致
	4、质检统计质检项平均分破W
	5、质检申诉审批-申诉修改，修改了以后点击保存没有任何提示，点击关闭没有反应，只能点右上角的X才可以关闭界面；
	6、部分质检员在二次质检申诉审批时，无法进行申诉修改，系统提示找不到对象。
	7、智能质检质检项与人工质检的智能质检查询质检项顺序不一致
---------------------------------------------------------------------------------------
模块名：    cc-quality
版本号:   3.0#20210203-1
修改日期: 2021-02-03
修改人:  潘以宝
说明:
	1、申诉修改页面无法打开录音，保存关闭按钮失效
---------------------------------------------------------------------------------------
模块名：      cc-quality
版本号:   3.0#20210128-1
修改日期: 2021-01-28
修改人:  潘以宝
说明:
	1、质检明显导出部分缺分数和到数据量扩大
---------------------------------------------------------------------------------------
模块名：      cc-quality
版本号:   3.0#20210127-1
修改日期: 2021-01-27
修改人:  潘以宝
说明:
	1、添加人工质检抽取记录
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20210127-1
修改日期: 2021-01-27
修改人:  王建
说明:
	1、优化人工质检结果，智能质检结果，质检结果明细excel导出
	2、补充部分国际化
	3、Oracle 统计质检得分等级 sql 质检规则列表 sql 报错
--------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20210126-2
修改日期: 2021-01-26
修改人:  欧诚剑
说明:
	1、新增excel导出工具类
	2、优化全媒体质检统计，质检任务统计，录音质检统计导出的数字需为数值类型，即能正常排序及求和
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20210126-1
修改日期: 2021-01-26
修改人:  潘以宝
说明:
	1、人工质检抽取日志
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20210119-2
修改日期: 2021-01-19
修改人:  王建
说明:
	1、质检申诉时间配置 日期范围改为天数范围 判断是否提供申诉按钮
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20210119-1
修改日期: 2021-01-19
修改人:  陈志彬
说明:
	1、新增全局页面引入JS脚本（ccbase  引入js脚本/JS_SCRIPT配置项）
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210116-2
修改日期: 2021-01-16
修改人:  潘以宝
说明:
	质检员随机抽取后对应通话/会话记录改成质检中状态
	质检后对应通话/会话记录改成待发布状态
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210116-1
修改日期: 2021-01-16
修改人:  潘以宝
说明:
	修改质检明细导出时在for中执行sql语句问题
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210115-1
修改日期: 2021-01-15
修改人:  潘以宝
说明:
	质检翻译问题
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210114-2
修改日期: 2021-01-14
修改人:  潘以宝
说明:
	质检统计layui浮点数排序乱序
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210114-1
修改日期: 2021-01-14
修改人:  潘以宝
说明:
	bug在质检管理-统计查询-统计图表页面的得分分布和分数等级只统计0-100的质检数据，不统计超过小于0或者大于100的质检数据，但实际中可能会出现小于0或者大于100的质检数据。
	bug:在质检管理-统计查询-人工质检结果页面的服务时间列显示为空，且通过服务时间查询结果不正确（查询结果比实际少）
	服务时间是新加字段，旧数据处理脚本为：
	update cc_qc_task_obj t1 INNER JOIN cc_call_record t2 on t1.SERIAL_ID=t2.SERIAL_ID set t1.SERVICE_TIME=t2.BILL_BEGIN_TIME where t1.SERIAL_ID=t2.SERIAL_ID;
	update cc_qc_task_obj t1 INNER JOIN cc_media_record t2 on t1.SERIAL_ID=t2.SERIAL_ID set t1.SERVICE_TIME=t2.BEGIN_TIME where t1.SERIAL_ID=t2.SERIAL_ID;
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210113-3
修改日期: 2021-01-13
修改人:  潘以宝
说明:
	1.v3做了兼容后质检和质检结果页面部分功能用不了（录音插件报错，导致无法播放录音，保存按钮没有刷新列表并关闭页面，关闭按钮无效，开启手动评分时全媒体质检页面报错）
	2.bug质检管理-统计查询-人工质检结果或质检管理-任务执行-我已质检页面只显示当天的质检数据，即使清空了查询条件，也只能查询当天的质检数据。
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210113-2
修改日期: 2021-01-13
修改人:  欧诚剑
说明:
	1.增加质检参数-申诉次数配置，质检申诉时间配置
	2.质检申诉对坐席数据查询时间范围控制
	3.质检申诉对申诉次数按企业做配置
---------------------------------------------------------------------------------------

模块名：      cc-quality.war
版本号:  3.0#20210113-1
修改日期: 2021-01-13
修改人:  潘以宝
说明:
	1.质检结果看今天-N天 之前的数据
	2.在质检管理-统计查询-全媒体质检统计页面点击"按日期"的单选按钮，在下面的表格点击"日期"或"人工质检平均分"或"待质检数"并没有按要求排序。
	3.在质检管理-统计查询-全媒体质检统计页面点击"按坐席"的单选按钮，如果坐席8068@dji账号设置了姓名为"大疆小美"，则坐席列会显示大疆小美-8068@dji和8068-8068@dji，且他们的质检数据不相同。
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210111-1
修改日期: 2021-01-11
修改人:  潘以宝
说明:
	1.任务执行，质检页面，高级查询，渠道类型查询条件下拉值不对，要与渠道配置中类型一致；小程序渠道类型显示为9，应显示为微信小程序
	2.在质检管理-统计查询-我的规则建议页面的操作没有处理按钮。需勾选权限'质检管理'>>'任务管理'>>'人工质检规则'>>'处理质检建议'
	3.抽检规则配置保存后，再次点击看不到之前配置
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210109
修改日期: 2021-01-09
修改人:  潘以宝
说明:
	1.优化质检-统计查询-全媒体质检统计中，“按坐席”和“按质检员”条件下，都是通过右边的“坐席”选项进行查找的，容易引起混淆，导致“按质检员”条件下不知道选择哪个选项，建议将“坐席”这个条件改成“查找对象”
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20210109
修改日期: 2021-01-09
修改人:  潘以宝
说明:
	1.优化质检-统计查询-全媒体质检统计中，“按坐席”和“按质检员”条件下，都是通过右边的“坐席”选项进行查找的，容易引起混淆，导致“按质检员”条件下不知道选择哪个选项，建议将“坐席”这个条件改成“查找对象”
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201230
修改日期: 2020-12-30
修改人:  秦朕
说明:
	1.全媒体质检页面提示语国际化
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201229
修改日期: 2020-12-29
修改人:  秦朕
说明:
	1.大疆一次验证不通过质检模块页面剩余国际化
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201218-1
修改日期: 2020-12-28
修改人:   潘以宝
说明:
	1.质检模块国际化
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201223-1
修改日期: 2020-12-23
修改人:   潘以宝
说明:
	1 如果没设置人员，则抽取所有人员的记录；
	2 质检人员必须设置，如未设置，在质检任务里无法选择该质检组。
	3 去掉呼叫结束原因的规则，话务默认只抽取clear_casue=0的记录
	4 保存质检规则描述， 在列表和添加任务页面显示
	5 添加质检规则排序字段，防止保存后抽取规则顺序改变

	脚本：
	alter table cc_qc_extract_column add COLUMN  INDEX_NUM int(10) DEFAULT '1' COMMENT '排序序号';
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201219-2
修改日期: 2020-12-19
修改人:   潘以宝
说明:
	1质检结果查找不配置限制时间时添加默认0
	2添加全媒体坐席质检数量限制
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201219-1
修改日期: 2020-12-19
修改人:   潘以宝
说明:
	1、第一次申诉,由原质检员审批;第二次申诉,有菜单权限的人都可以审批
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201218-1
修改日期: 2020-12-18
修改人:   潘以宝
说明:
	1、人工质检结果列表 时间组件限制时间时如果没有配置 参数配置里的 质检结果配置查找时间限制会导致渲染成NaN-NaN-NaN
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201217-1
修改日期: 2020-12-17
修改人:   欧诚剑
说明:
	1、v2质检更改的界面整理到v3版本里面来
	qc-class-advice-list.jsp
	qc-class-media-advice-detail.jsp
	qc-class-media-advice-handle.jsp
	qc-record-checked.jsp
	qc-record-Input.jsp
	qc-result-checked.jsp，qc-result-Input.jsp，qc-task-extract.jsp，qc-task-voice-list.jsp
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201217-1
修改日期: 2020-12-17
修改人:   潘以宝
说明:
	1、系统在抽取质检数据时保存服务时间（通话或会话开始时间）,格式如2020-12-12 12:00:00
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201217-1
修改日期: 2020-12-17
修改人:   欧诚剑
说明:
	1、质检结果明细通话/会话记录ID显示bug，以及查询sql问题
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201216-1
修改日期: 2020-12-16
修改人:   潘以宝
说明:
	1、录音添加坐席质检最大数限制
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201210-1
修改日期: 2020-12-10
修改人:   潘以宝
说明:
	1、人工质检结果，服务时间按天、按周、按月管理的时间框错误
	2、IE11 抽取规则样式错乱
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201207-1
修改日期: 2020-12-07
修改人:   潘以宝
说明:
	1、将质检的定时任务更换为标准版的定时任务
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:  3.0#20201204-1
修改日期: 2020-12-01
修改人:   张雪飞
说明:
	1、 新增是否开启二次申诉开关
	2、 新增 质检页面批量释放按钮
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20201127-1
修改日期: 2020-11-27
修改人:   张雪飞
说明:
	1、 申述审批退回原因取值错误;
	2、 质检条件抽取数据查询不到质检组人员;
	3、bug4710 人工质检分组，质检对象和质检员无数据显示;
	4、bug4657 任务执行，质检界面，按条件抽取，选择质检组后，坐席字段无选择数据;
---------------------------------------------------------------------------------------

模块名：      cc-quality.war
版本号:   3.0#20201103-1
修改日期: 2020-11-03
修改人:   丁泉铭
说明:
1、bug4664质检申诉审批和质检二次申诉审批，退回界面无提示语
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200827-1
修改日期: 2020-08-27
修改人:   潘以宝
说明:
1、在申诉退回时，需要填写退回原因，在申诉内容时间轴，需要能看到退回原因
2、在任务执行界面，修改按钮通过功能权限控制，而且需要限制，提交后X小时就不能看到修改按钮了（X小时做成配置项，默认一小时）
3、录音+全媒体质检统计里，按坐席统计时，要再加上汇总的平均分或者是总分（类似报表，在最底部加一列汇总列）
4、bug3661智能质检结果，质检结果明细数据未显示
5、bug3611质检任务，新增任务界面，抽取规则信息用户提示看不懂
6、bug3623录音质检统计，导出数据有误
7、bug3663全媒体质检统计，导出数据有误
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200813-1
修改日期: 2020-08-13
修改人:   潘以宝
说明:
1、任务执行，时间默认为近三个月，目前是一周
2、语音质检执行界面排列优化
3、人工质检结果-质检结果明细-选择语音任务，显示典项录音列和查询条件，查询条件需要排列整齐
4、人工质检结果-质检结果明细-选择语音任务，显示主叫、被叫、开始时间、结束时间、满意度，以及查询条件（主叫、被叫、开始时间、满意度）
5、人工质检结果-质检结果明细-选择语音任务，显示客户标识、开始时间、结束时间、满意度，以及查询条件（客户标识、开始时间、满意度）
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200810-1
修改日期: 2020-08-10
修改人:   潘以宝
说明:
bug3014:【质检】质检申诉界面中的质检结果搜索条件无效
bug3015:【质检】质检结果明细报表中，质检申诉状态显示不正确
bug3328:【质检】已使用的智能质检规则可以进行删除
bug3323:【质检】建议质检表界面添加字段说明和该字段的算法说明
bug3307:【质检】全媒体质检统计报表数据显示不正确
bug3015:【质检】质检结果明细报表中，质检申诉状态显示不正确
bug3014: 【质检】质检申诉界面中的质检结果搜索条件无效
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200804-1
修改日期: 2020-08-04
修改人:   潘以宝
说明:
   	解决录音文件读取错误
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200803-1
修改日期: 2020-08-03
修改人:   潘以宝
说明:
   	解决录音文件需要登录才能获取
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200730-1
修改日期: 2020-07-30
修改人:   潘以宝
说明:
   	添加允许允许时间配置项
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200722-1
修改日期: 2020-07-22
修改人:   潘以宝
说明:
   	添加立即执行
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200717-1
修改日期: 2020-07-17
修改人:   潘以宝
说明:
   	添加待质检列表
   	ALTER TABLE cc_qc_task_obj ADD `INSPECTOR_TIME` varchar(19) DEFAULT NULL COMMENT '人工抽检时间';
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200707-1
修改日期: 2020-07-07
修改人:   潘以宝
说明:
   1,添加xss后之间规则部分特殊字符判断错误
   2,质检模块增加配置项，控制一条录音是否只能被一个任务抽取
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200628-1
修改日期: 2020-06-30
修改人:   潘以宝
说明:
   1，质检数据表添加质检员字段，标记是哪个质检抽取的数据
   ALTER TABLE cc_qc_task_obj ADD `INSPECTOR` varchar(64) DEFAULT NULL COMMENT '质检员，任务抽取质检时为空的进行质检，质检员和管理员可以置空，管理员可以重新指定';
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200628-1
修改日期: 2020-06-28
修改人:   潘以宝
说明:
   1.视源录音转写无法显示
   2.标准版质检执行，抽取数据时，增加抽取条件，如日期、时间、通话时长、坐席、班组、方向、满意度
   3.质检优化： 1、播放录音的地方，需要支持调整倍速，0.5x，0.75x,1.0x、1.25x、1.5x、20x 2、单个人工质检规则里的指标以及细项需要支持导入导出
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200628-1
修改日期: 2020-06-12
修改人:   潘以宝
说明:
   1，质检国际化
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200526-1
修改日期: 2020-05-26
修改人:   潘以宝
说明:
   1，质检数据抽取，在页面增加按工号抽取数据的条件
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200429-1
修改日期: 2020-02-29
修改人:   潘以宝
说明:
   1，质检优化：菜单命名优化，质检目标率完善，坐席抽取规则优化，增加任务释放功能
    执行脚本
   DELETE FROM cc_qc_column WHERE ID='207'
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200428-1
修改日期: 2020-04-28
修改人:   潘以宝
说明:
   1，智能质检脚本进行优化，模块配合进行调整（完善表的创建人信息，质检项分数使用两位小数）
   执行脚本
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200424-1
修改日期: 2020-04-24
修改人:   潘以宝
说明:
   1，质检项名称太长报错
   2，添加申诉日志记录
   3，添加质检统计图表
   执行脚本
   ALTER TABLE CC_QC_RESULT_ITEM MODIFY ITEM_NAME VARCHAR(100);

   CREATE TABLE `cc_qc_reconsider_log` (
  `ID` varchar(64) NOT NULL COMMENT 'ID',
  `QC_REC_ID` varchar(32) NOT NULL COMMENT '复议ID',
  `SCORE` int(11) DEFAULT NULL COMMENT '分数，只记录一次申请，二次申请，确认，结案时分数',
  `STATE` int(11) DEFAULT NULL COMMENT '操作 1一次申请，2一次修改分数（审核通过），3一次不通过，4二次申请，5二次修改分数（审核通过），6二次审核不通过，7确认，99结案',
  `CREATE_ACC` varchar(30) DEFAULT NULL COMMENT '创建人账号',
  `CREATE_TIME` varchar(19) DEFAULT NULL COMMENT '创建时间',
  `ENT_ID` varchar(30) DEFAULT NULL COMMENT '所属企业',
  `BUSI_ORDER_ID` varchar(64) DEFAULT NULL COMMENT '订购ID',
  PRIMARY KEY (`ID`),
  KEY `CC_QC_RECONSIDER_LOG_1` (`QC_REC_ID`),
  KEY `CC_QC_RECONSIDER_LOG_2` (`ENT_ID`,`BUSI_ORDER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='质检申诉记录';
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200320-1
修改日期: 2020-03-20
修改人:   潘以宝
说明:
   1、质检规则里的，质检分类、质检组要增加排序功能，所有展示质检规则的地方要按序号排序
   2、质检执行，在抽取质检任务时，可以在该任务的抽取规则上，进行二次条件筛选
   3、质检任务编辑界面，在智能质检规则旁边，增加智能质检规则配置的入口
   4、质检结果改为“人工质检结果"，质检结果明细，不需要用单独的菜单，入口放到人工质检结果里；
   5、增加”智能质检结果“，根据任务，查下任务下所有智能质检结果明细
   6、人工质检结果明细表分页错误
   7、开启手动评分的情况下质检申诉流程不可用，保存质检结果保存，质检项评分被清空
   执行脚本
   ALTER TABLE CC_QC_CLASS ADD COLUMN  `INDEX_NUM` int(10) DEFAULT 1 COMMENT '排序序号';
   ALTER TABLE CC_QC_ITEM ADD COLUMN  `INDEX_NUM` int(10) DEFAULT 1 COMMENT '排序序号';
   ALTER TABLE CC_QC_RESULT_ITEM ADD COLUMN  `INDEX_NUM` int(10) DEFAULT 1 COMMENT '排序序号';
   update c_cf_dictgroup set TYPE='2' where CODE in('QC_ZN_RULE');
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200228-1
修改日期: 2020-02-28
修改人:   潘以宝
说明:
   1，质检结果明细日期查找条件错误；
   2，语音质检申诉分数不正确；
   3，质检结果，质检结果明细导出错误
---------------------------------------------------------------------------------------
模块名：      cc-quality.war
版本号:   3.0#20200224-1
修改日期: 2020-02-24
修改人:   潘以宝
说明:
不开启手动评分 语音质检详细页面：
质检：qc-record-checked.jsp
查看详情：qc-result-voice-detail.jsp
结果修改，申诉修改：qc-result-voice-checked.jsp

不开启手动评分 全媒体质检详细页面：
质检：media-qc-checked.jsp
查看详情：qc-result-media-detail.jsp
结果修改，申诉修改：qc-result-media-checked.jsp

开启手动评分 语音质检详细页面：
质检：qc-record-Input.jsp
查看详情：qc-result-voice-detail.jsp
结果修改: qc-result-Input.jsp

开启手动评分 全媒体质检详细页面：
质检：media-qc-Input.jsp
查看详情：qc-result-media-detail.jsp
结果修改: qc-result-Input.jsp
---------------------------------------------------------------------------------------


