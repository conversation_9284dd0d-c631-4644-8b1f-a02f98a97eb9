-- 按日查询数据
 select ENT_ID,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>SSUEATION,DATE_VA,SERVICEINAPPROPRIATELANGUAGE,<PERSON>MPRO<PERSON><PERSON><PERSON><PERSON>TU<PERSON>,INTENTION<PERSON><PERSON>NGUP,INCOR<PERSON>CTPRONUNCIATION,INCONSISTENTSPEECHRA<PERSON>,UNF<PERSON>END<PERSON>YTONE,IMPRO<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>HRASE,LACKOFREASSURINGWORDS,LACKOFP<PERSON><PERSON><PERSON>WOR<PERSON>,IMPRO<PERSON><PERSON><PERSON>NGUAGE,UN<PERSON><PERSON><PERSON>RUNRAISEDISSUE,LACKOFP<PERSON><PERSON><PERSON><PERSON>,FIRSTRESPONSEDELAY,RESPONSEDELAY,LACKOFCOM<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,LACKOFCLARI<PERSON>INEXPRESSION,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>MA<PERSON><PERSON>,INCORRECTGUI<PERSON>NCE,PRO<PERSON>DI<PERSON>NEGA<PERSON>VEINFORMATION,LAC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>RESPONSE,NOPROACTIVEINFORMATIONREQUEST,INCOMPLE<PERSON>ANSW<PERSON>,I<PERSON><PERSON><PERSON>VA<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ORMA<PERSON>ON,NOPROPERSOLUTIONPROVIDED,CORRECTINFORMATIONFOREVENTISSUES,ESCALATIONCOMPLAINTNOTREPORTEDONTIME,SHIRKINGRESPONSIBILITY,MISSEDINFORMATIONANDCASUALRESPONSE,DATAENTRYFORMATERROR,CRITICALDATAENTRYFORMATERROR,PROPERORDERENTRY,SECONDCOMPLAINTNOTVERIFIED,CALLENVIRONMENT from (select t1.*,cc_dim_date.DATE_VALUE DATE_VA from ayykefu_stat.QC_CHECK_SUMMARY2 t1
left join ayykefu_stat.cc_dim_date cc_dim_date on t1.DATE_ID = cc_dim_date.DATE_ID
where t1.DATE_ID >= 20250701 and t1.DATE_ID <= 20250731
group by cc_dim_date.DATE_VALUE
order by t1.DATE_ID desc) temp  where 1=1  LIMIT 0,20

-- 按周查询数据
select ENT_ID,DATE_ID,REPEATEDISSUEATION,DATE_VA,SERVICEINAPPROPRIATELANGUAGE,IMPROPERATTITUDE,INTENTIONALHANGUP,INCORRECTPRONUNCIATION,INCONSISTENTSPEECHRATE,UNFRIENDLYTONE,IMPROPERFAREWELLPHRASE,LACKOFREASSURINGWORDS,LACKOFPOLITEWORDS,IMPROPERLANGUAGE,UNCLEARORUNRAISEDISSUE,LACKOFPATIENCE,FIRSTRESPONSEDELAY,RESPONSEDELAY,LACKOFCOMMUNICATIONSKILLS,LACKOFCLARITYINEXPRESSION,RIGIDRESPONSE,DISCLOSUREOFIRRELEVANTINFORMATION,INCORRECTGUIDANCE,PROVIDINGNEGATIVEINFORMATION,LACKOFPROACTIVERESPONSE,NOPROACTIVEINFORMATIONREQUEST,INCOMPLETEANSWER,IRRELEVANTANSWER,LACKOFHANDLINGSKILLS,CORRECTGUIDANCEONOTHERISSUES,WRONGGUIDANCE,PROVIDINGINCORRECTINFORMATION,NOPROPERSOLUTIONPROVIDED,CORRECTINFORMATIONFOREVENTISSUES,ESCALATIONCOMPLAINTNOTREPORTEDONTIME,SHIRKINGRESPONSIBILITY,MISSEDINFORMATIONANDCASUALRESPONSE,DATAENTRYFORMATERROR,CRITICALDATAENTRYFORMATERROR,PROPERORDERENTRY,SECONDCOMPLAINTNOTVERIFIED,CALLENVIRONMENT from (select t1.*,cc_dim_date.WEEK_IN_YEAR DATE_VA from ayykefu_stat.QC_CHECK_SUMMARY2 t1
left join ayykefu_stat.cc_dim_date cc_dim_date on t1.DATE_ID = cc_dim_date.DATE_ID
where t1.DATE_ID >= 20250701 and t1.DATE_ID <= 20250731
group by cc_dim_date.WEEK_IN_YEAR
order by t1.DATE_ID desc) temp  where 1=?  LIMIT 0,20

-- 按月查询数据
select ENT_ID,DATE_ID,REPEATEDISSUEATION,DATE_VA,SERVICEINAPPROPRIATELANGUAGE,IMPROPERATTITUDE,INTENTIONALHANGUP,INCORRECTPRONUNCIATION,INCONSISTENTSPEECHRATE,UNFRIENDLYTONE,IMPROPERFAREWELLPHRASE,LACKOFREASSURINGWORDS,LACKOFPOLITEWORDS,IMPROPERLANGUAGE,UNCLEARORUNRAISEDISSUE,LACKOFPATIENCE,FIRSTRESPONSEDELAY,RESPONSEDELAY,LACKOFCOMMUNICATIONSKILLS,LACKOFCLARITYINEXPRESSION,RIGIDRESPONSE,DISCLOSUREOFIRRELEVANTINFORMATION,INCORRECTGUIDANCE,PROVIDINGNEGATIVEINFORMATION,LACKOFPROACTIVERESPONSE,NOPROACTIVEINFORMATIONREQUEST,INCOMPLETEANSWER,IRRELEVANTANSWER,LACKOFHANDLINGSKILLS,CORRECTGUIDANCEONOTHERISSUES,WRONGGUIDANCE,PROVIDINGINCORRECTINFORMATION,NOPROPERSOLUTIONPROVIDED,CORRECTINFORMATIONFOREVENTISSUES,ESCALATIONCOMPLAINTNOTREPORTEDONTIME,SHIRKINGRESPONSIBILITY,MISSEDINFORMATIONANDCASUALRESPONSE,DATAENTRYFORMATERROR,CRITICALDATAENTRYFORMATERROR,PROPERORDERENTRY,SECONDCOMPLAINTNOTVERIFIED,CALLENVIRONMENT from (select t1.*,cc_dim_date.MONTH_IN_YEAR DATE_VA from ayykefu_stat.QC_CHECK_SUMMARY2 t1
left join ayykefu_stat.cc_dim_date cc_dim_date on t1.DATE_ID = cc_dim_date.DATE_ID
where t1.DATE_ID >= 20250701 and t1.DATE_ID <= 20250731
group by cc_dim_date.MONTH_IN_YEAR
order by t1.DATE_ID desc) temp  where 1=1  LIMIT 0,20

