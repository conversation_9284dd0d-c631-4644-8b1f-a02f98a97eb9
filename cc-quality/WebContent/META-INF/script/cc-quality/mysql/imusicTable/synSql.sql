-- 数据汇总同步sql案例
INSERT INTO #stat#.#QC_CHECK_SUMMARY# (
    ENT_ID,
    DATE_ID,
    ServiceInappropriateLanguage,
    ImproperAttitude,
    IntentionalHangUp,
    IncorrectPronunciation,
    InconsistentSpeechRate,
    UnfriendlyTone,
    ImproperFarewellPhrase,
    LackOfReassuringWords,
    LackOfPoliteWords,
    ImproperLanguage,
    UnclearOrUnraisedIssue,
    LackOfPatience,
    RepeatedIssueation,
    FirstResponseDelay,
    ResponseDelay,
    LackOfCommunicationSkills,
    LackOfClarityInExpression,
    RigidResponse,
    DisclosureOfIrrelevantInformation,
    IncorrectGuidance,
    ProvidingNegativeInformation,
    LackOfProactiveResponse,
    NoProactiveInformationRequest,
    IncompleteAnswer,
    IrrelevantAnswer,
    LackOfHandlingSkills,
    CorrectGuidanceOnOtherIssues,
    WrongGuidance,
    ProvidingIncorrectInformation,
    NoProperSolutionProvided,
    CorrectInformationForEventIssues,
    EscalationComplaintNotReportedOnTime,
    ShirkingResponsibility,
    MissedInformationAndCasualResponse,
    DataEntryFormatError,
    CriticalDataEntryFormatError,
    ProperOrderEntry,
    SecondComplaintNotVerified,
    CallEnvironment
)

SELECT

    #entId# ENT_ID,
    cc_dim_date.DATE_ID  AS DATE_ID,
    SUM(a.ITEM_NAME = #apos#出现服务禁语#apos#) AS `ServiceInappropriateLanguage`,
    SUM(a.ITEM_NAME = #apos#态度不规范#apos#) AS `ImproperAttitude`,
    SUM(a.ITEM_NAME = #apos#故意挂机#apos#) AS `IntentionalHangUp`,
    SUM(a.ITEM_NAME = #apos#发音不准#apos#) AS `IncorrectPronunciation`,
    SUM(a.ITEM_NAME = #apos#语速不匹配#apos#) AS `InconsistentSpeechRate`,
    SUM(a.ITEM_NAME = #apos#语气不友善#apos#) AS `UnfriendlyTone`,
    SUM(a.ITEM_NAME = #apos#欢送语不规范#apos#) AS `ImproperFarewellPhrase`,
    SUM(a.ITEM_NAME = #apos#缺少安抚语#apos#) AS `LackOfReassuringWords`,
    SUM(a.ITEM_NAME = #apos#缺少礼貌语#apos#) AS `LackOfPoliteWords`,
    SUM(a.ITEM_NAME = #apos#用语不规范#apos#) AS `ImproperLanguage`,
    SUM(a.ITEM_NAME = #apos#问题不清晰没有提出#apos#) AS `UnclearOrUnraisedIssue`,
    SUM(a.ITEM_NAME = #apos#欠缺耐心#apos#) AS `LackOfPatience`,
    SUM(a.ITEM_NAME = #apos#重复确认问题#apos#) AS `RepeatedIssueation`,
    SUM(a.ITEM_NAME = #apos#首次应答超时#apos#) AS `FirstResponseDelay`,
    SUM(a.ITEM_NAME = #apos#应答超时#apos#) AS `ResponseDelay`,
    SUM(a.ITEM_NAME = #apos#沟通欠技巧#apos#) AS `LackOfCommunicationSkills`,
    SUM(a.ITEM_NAME = #apos#表述欠条理#apos#) AS `LackOfClarityInExpression`,
    SUM(a.ITEM_NAME = #apos#应答生硬#apos#) AS `RigidResponse`,
    SUM(a.ITEM_NAME = #apos#透露不相关资料#apos#) AS `DisclosureOfIrrelevantInformation`,
    SUM(a.ITEM_NAME = #apos#指引错误#apos#) AS `IncorrectGuidance`,
    SUM(a.ITEM_NAME = #apos#提供负面信息#apos#) AS `ProvidingNegativeInformation`,
    SUM(a.ITEM_NAME = #apos#应答不够主动#apos#) AS `LackOfProactiveResponse`,
    SUM(a.ITEM_NAME = #apos#没有主动让用户提供信息#apos#) AS `NoProactiveInformationRequest`,
    SUM(a.ITEM_NAME = #apos#回答不全面#apos#) AS `IncompleteAnswer`,
    SUM(a.ITEM_NAME = #apos#答非所问#apos#) AS `IrrelevantAnswer`,
    SUM(a.ITEM_NAME = #apos#处理欠技巧#apos#) AS `LackOfHandlingSkills`,
    SUM(a.ITEM_NAME = #apos#其他问题正确指引#apos#) AS `CorrectGuidanceOnOtherIssues`,
    SUM(a.ITEM_NAME = #apos#错误指引#apos#) AS `WrongGuidance`,
    SUM(a.ITEM_NAME = #apos#提供错误信息#apos#) AS `ProvidingIncorrectInformation`,
    SUM(a.ITEM_NAME = #apos#未提供妥善处理方案#apos#) AS `NoProperSolutionProvided`,
    SUM(a.ITEM_NAME = #apos#活动问题提供正确信息#apos#) AS `CorrectInformationForEventIssues`,
    SUM(a.ITEM_NAME = #apos#升级投诉未及时上报#apos#) AS `EscalationComplaintNotReportedOnTime`,
    SUM(a.ITEM_NAME = #apos#推诿#apos#) AS `ShirkingResponsibility`,
    SUM(a.ITEM_NAME = #apos#漏查信息&随意作答#apos#) AS `MissedInformationAndCasualResponse`,
    SUM(a.ITEM_NAME = #apos#录入格式错误#apos#) AS `DataEntryFormatError`,
    SUM(a.ITEM_NAME = #apos#录入格式错误(致命)#apos#) AS `CriticalDataEntryFormatError`,
    SUM(a.ITEM_NAME = #apos#工单录入规范#apos#) AS `ProperOrderEntry`,
    SUM(a.ITEM_NAME = #apos#二次投诉用户未核实前单内容#apos#) AS `SecondComplaintNotVerified`,
    SUM(a.ITEM_NAME = #apos#通话环境#apos#) AS `CallEnvironment`

FROM
    #ycbusi#.cc_qc_result_item a
        JOIN #ycbusi#.cc_qc_result b ON a.QC_RESULT_ID = b.QC_RESULT_ID
        left join  #stat#.cc_dim_date cc_dim_date  on substr(b.QC_TIME, 1, 10) = cc_dim_date.DATE_VALUE
WHERE
    b.QC_TIME >= #beginTime#
  AND b.QC_TIME <= #endTime#
GROUP BY
    cc_dim_date.DATE_ID
ORDER BY
    cc_dim_date.DATE_ID